<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打开京东小程序</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }

        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .open-btn {
            background-color: #e93323;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .open-btn:hover {
            background-color: #d12c1f;
        }

        .open-btn:active {
            transform: translateY(1px);
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
        }

        .note {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>京东小程序启动器</h1>
        <button class="open-btn" onclick="openJDApp()">打开京东小程序</button>
        <p><a class="alipayButton" href="alipays://platformapi/startapp?appId=2021001107610820&amp;query=mobile%3D15635050543" rel="noopener noreferrer">点击跳转到「支付宝」支付</a></p>
        <div class="note">
            点击按钮将尝试打开京东APP中的小程序
        </div>
    </div>

    <script>
        function openJDApp() {
            const jdUrl = "alipays://platformapi/startapp?appId=20000067&url=https%3A%2F%2Fpages.tmall.com%2Fwow%2Fgo%2Fmx-growth%2Fsec-flow%2F9ee43cea47e04fa79a486e2d14b0de88alipays://platformapi/startapp?appId=2021001107610820&amp;query=mobile%3D15635050543";

            try {
                window.location.href = jdUrl;
            } catch (error) {
                console.error('打开应用失败:', error);
                alert('无法打开京东APP，请确保已安装京东APP');
            }
        }
    </script>
</body>
</html>