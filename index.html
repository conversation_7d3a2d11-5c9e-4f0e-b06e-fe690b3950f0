<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打开京东小程序</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }

        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .open-btn {
            background-color: #e93323;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .open-btn:hover {
            background-color: #d12c1f;
        }

        .open-btn:active {
            transform: translateY(1px);
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
        }

        .note {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>京东小程序启动器</h1>
        <button class="open-btn" onclick="openJDApp()">打开京东小程序</button>
        <div class="note">
            点击按钮将尝试打开京东APP中的小程序
        </div>
    </div>

    <script>
        function openJDApp() {
            const jdUrl = "openapp.jdmobile://virtual?params={%22category%22:%22jump%22,%22des%22:%22jdmp%22,%22appId%22:%226B2A83645B60FDA3D223B261AF9EFD04%22,%22vapptype%22:%221%22,%22path%22:%22pages/index/index.html%22,%22pageAlias%22:%22%22,%22scene%22:%22applets_share_back%22,%22mpMode%22:%22%22,%22param%22:{%22pageName%22:%22home%22,%22release%22:%22online%22,%22phoneNo%22:%2218837452595%22,%22facePrice%22:%22100%22}}&sign=KQL4II828PHXRLJ6MICN1UN04G5C0XK2";

            try {
                window.location.href = jdUrl;
            } catch (error) {
                console.error('打开应用失败:', error);
                alert('无法打开京东APP，请确保已安装京东APP');
            }
        }
    </script>
</body>
</html>